<script setup>

import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';
import 'leaflet-routing-machine';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    provinces: {
        type: Array,
        required: true,
        default: () => [],
    },
    districts: {
        type: Array,
        required: true,
        default: () => [],
    },
});

// Map and UI state
const mapContainer = ref(null);
const map = ref(null);
const panelsVisible = ref(true);

// Map selection state
const mapSelectionMode = ref(null); // 'from', 'to', or throughIndex
const hoverCoordinates = ref(null);
const hoverLocationName = ref('');
const isLoadingHoverLocation = ref(false);

// Navigation form state
const navigationForm = ref({
    from: {
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    },
    to: {
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    },
    through: [],
    mode: 'car',
    language: 'en'
});

// Search state
const searchResults = ref({
    from: [],
    to: [],
    through: {}
});
const isSearching = ref({
    from: false,
    to: false,
    through: {}
});

// Map markers
const markers = ref({
    from: null,
    to: null,
    through: []
});

// Navigation results
const navigationResults = ref(null);
const isNavigating = ref(false);
const selectedRouteIndex = ref(0);
const showDirections = ref(false);

// Leaflet Routing Machine control
const routingControl = ref(null);

// Map configuration for Rwanda
const MAP_CONFIG = {
    center: [-1.9403, 29.8739], // Rwanda center [latitude, longitude]
    zoom: 8,
    minZoom: 7,
    maxZoom: 18,
    bounds: [
        [-2.9389, 28.8617], // Southwest coordinates [lat, lng]
        [-1.0474, 30.8997]  // Northeast coordinates [lat, lng]
    ]
};

// Computed properties
const canSubmit = computed(() => {
    return navigationForm.value.from.latitude &&
           navigationForm.value.from.longitude &&
           navigationForm.value.to.latitude &&
           navigationForm.value.to.longitude;
});

const throughPointsOrdered = computed(() => {
    return [...navigationForm.value.through].sort((a, b) => a.index - b.index);
});

// Search functions
const performLocationSearch = debounce(async (type, query, throughIndex = null) => {
    if (query.length < 3) {
        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = [];
        } else {
            searchResults.value[type] = [];
        }
        return;
    }

    if (throughIndex !== null) {
        isSearching.value.through[throughIndex] = true;
    } else {
        isSearching.value[type] = true;
    }

    try {
        const response = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang: navigationForm.value.language,
            filterData: 'all'
        });

        const allResults = [];
        ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'].forEach(resultType => {
            if (response.data[resultType]) {
                response.data[resultType].forEach(item => {
                    allResults.push({
                        ...item,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                        type: resultType
                    });
                });
            }
        });

        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = allResults;
        } else {
            searchResults.value[type] = allResults;
        }
    } catch (error) {
        console.error('Search error:', error);
        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = [];
        } else {
            searchResults.value[type] = [];
        }
    } finally {
        if (throughIndex !== null) {
            isSearching.value.through[throughIndex] = false;
        } else {
            isSearching.value[type] = false;
        }
    }
}, 300);

// Map selection functions
const enableMapSelection = (type, throughIndex = null) => {
    mapSelectionMode.value = throughIndex !== null ? throughIndex : type;
    map.value.getCanvas().style.cursor = 'crosshair';

    // Show instruction message
    const instruction = throughIndex !== null
        ? `Click on the map to select stop ${throughIndex + 1} location`
        : `Click on the map to select ${type} location`;
    console.log(instruction);
};

const disableMapSelection = () => {
    mapSelectionMode.value = null;
    if (map.value) {
        map.value.getCanvas().style.cursor = '';
    }
    hoverCoordinates.value = null;
    hoverLocationName.value = '';
};

const queryLocationName = async (lat, lng) => {
    try {
        isLoadingHoverLocation.value = true;
        const response = await axios.post('/map/search-latitude-langitude-json', {
            latitude: lat,
            longitude: lng,
            lang: navigationForm.value.language
        });

        if (response.data && response.data.length > 0) {
            const location = response.data[0];
            return location.name_en || location.name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } catch (error) {
        console.error('Error querying location name:', error);
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } finally {
        isLoadingHoverLocation.value = false;
    }
};

// Location selection functions
const selectLocation = (type, location, throughIndex = null) => {
    // Validate location data
    if (!location || typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {
        console.error('Invalid location data:', location);
        return;
    }

    if (throughIndex !== null) {
        // Handle through point selection
        const throughPoint = navigationForm.value.through.find(p => p.index === throughIndex);
        if (throughPoint) {
            throughPoint.latitude = location.latitude;
            throughPoint.longitude = location.longitude;
            throughPoint.name = location.name_en || location.name || 'Unknown location';
            throughPoint.searchQuery = '';
        }
        searchResults.value.through[throughIndex] = [];
        updateThroughMarker(throughIndex, location.latitude, location.longitude);
    } else {
        // Handle from/to selection
        navigationForm.value[type].latitude = location.latitude;
        navigationForm.value[type].longitude = location.longitude;
        navigationForm.value[type].name = location.name_en || location.name || 'Unknown location';
        navigationForm.value[type].searchQuery = '';
        searchResults.value[type] = [];
        updateMarker(type, location.latitude, location.longitude);
    }

    // Update map view
    try {
        if (map.value && location.latitude && location.longitude) {
            map.value.flyTo([location.latitude, location.longitude], 15, {
                duration: 1
            });
        }
    } catch (error) {
        console.error('Error flying to location:', error);
    }
};

const selectLocationFromMap = async (lat, lng, type, throughIndex = null) => {
    const locationName = await queryLocationName(lat, lng);

    const location = {
        latitude: lat,
        longitude: lng,
        name: locationName
    };

    selectLocation(type, location, throughIndex);
    disableMapSelection();
};

const clearLocation = (type, throughIndex = null) => {
    // Disable map selection if it's active for this location
    if ((throughIndex !== null && mapSelectionMode.value === throughIndex) ||
        (throughIndex === null && mapSelectionMode.value === type)) {
        disableMapSelection();
    }

    if (throughIndex !== null) {
        // Remove through point
        navigationForm.value.through = navigationForm.value.through.filter(p => p.index !== throughIndex);
        delete searchResults.value.through[throughIndex];
        delete isSearching.value.through[throughIndex];
        removeThroughMarker(throughIndex);
        reorderThroughPoints();
    } else {
        // Clear from/to
        navigationForm.value[type] = {
            latitude: null,
            longitude: null,
            name: '',
            searchQuery: ''
        };
        searchResults.value[type] = [];
        removeMarker(type);
    }

    // Clear navigation results when locations change
    if (navigationResults.value) {
        clearNavigation();
    }
};

// Through points management
const addThroughPoint = () => {
    const newIndex = navigationForm.value.through.length > 0
        ? Math.max(...navigationForm.value.through.map(p => p.index)) + 1
        : 0;

    navigationForm.value.through.push({
        index: newIndex,
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    });

    searchResults.value.through[newIndex] = [];
    isSearching.value.through[newIndex] = false;
};

const reorderThroughPoints = () => {
    navigationForm.value.through.forEach((point, index) => {
        point.index = index;
    });
};

// Map marker management
const updateMarker = (type, lat, lng) => {
    if (markers.value[type]) {
        map.value.removeLayer(markers.value[type]);
    }

    const color = type === 'from' ? '#22c55e' : '#ef4444'; // Green for from, red for to
    markers.value[type] = L.marker([lat, lng], {
        icon: L.divIcon({
            className: 'custom-marker',
            html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        })
    }).addTo(map.value);
};

const removeMarker = (type) => {
    if (markers.value[type]) {
        map.value.removeLayer(markers.value[type]);
        markers.value[type] = null;
    }
};

const updateThroughMarker = (index, lat, lng) => {
    // Remove existing marker if any
    const existingMarkerIndex = markers.value.through.findIndex(m => m.index === index);
    if (existingMarkerIndex !== -1) {
        map.value.removeLayer(markers.value.through[existingMarkerIndex].marker);
        markers.value.through.splice(existingMarkerIndex, 1);
    }

    // Add new marker
    const marker = L.marker([lat, lng], {
        icon: L.divIcon({
            className: 'custom-marker',
            html: `<div style="background-color: #3b82f6; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        })
    }).addTo(map.value);

    markers.value.through.push({ index, marker });
};

const removeThroughMarker = (index) => {
    const markerIndex = markers.value.through.findIndex(m => m.index === index);
    if (markerIndex !== -1) {
        map.value.removeLayer(markers.value.through[markerIndex].marker);
        markers.value.through.splice(markerIndex, 1);
    }
};

// Map initialization
const initializeMap = () => {
    // Initialize Leaflet map
    map.value = L.map(mapContainer.value, {
        center: MAP_CONFIG.center,
        zoom: MAP_CONFIG.zoom,
        minZoom: MAP_CONFIG.minZoom,
        maxZoom: MAP_CONFIG.maxZoom,
        maxBounds: MAP_CONFIG.bounds,
        maxBoundsViscosity: 1.0
    });

    // Add OpenStreetMap tile layer with Rwanda bounds
    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap contributors',
        bounds: MAP_CONFIG.bounds // Restrict tiles to Rwanda area
    }).addTo(map.value);

    // Ensure map stays within Rwanda bounds
    map.value.on('drag', function() {
        map.value.panInsideBounds(MAP_CONFIG.bounds, { animate: false });
    });

    // Set initial view to fit Rwanda
    map.value.fitBounds(MAP_CONFIG.bounds, { padding: [10, 10] });

    // Add Rwanda boundary visualization
    const rwandaBounds = L.rectangle(MAP_CONFIG.bounds, {
        color: '#20B2AA',
        weight: 2,
        fillOpacity: 0.1,
        fillColor: '#20B2AA'
    }).addTo(map.value);

    // Add click handler for coordinate selection
    map.value.on('click', handleMapClick);

    // Add hover handler for coordinate preview
    map.value.on('mousemove', handleMapHover);
    map.value.on('mouseleave', handleMapMouseLeave);

    // Prevent map from hiding form panels
    map.value.getContainer().style.zIndex = '1';

    // Debug: Log when map is ready
    console.log('Map initialized, panelsVisible:', panelsVisible.value);
};

// Handle map clicks for coordinate selection
const handleMapClick = async (e) => {
    try {
        const { lng, lat } = e.latlng;

        if (mapSelectionMode.value !== null) {
            if (typeof mapSelectionMode.value === 'number') {
                // Through point selection
                await selectLocationFromMap(lat, lng, 'through', mapSelectionMode.value);
            } else {
                // From/To selection
                await selectLocationFromMap(lat, lng, mapSelectionMode.value);
            }
        }
    } catch (error) {
        console.error('Error handling map click:', error);
    }
};

// Handle map hover for coordinate preview
const handleMapHover = (e) => {
    try {
        if (mapSelectionMode.value !== null) {
            const { lng, lat } = e.latlng;
            hoverCoordinates.value = { latitude: lat, longitude: lng };

            // Debounced location name query
            clearTimeout(hoverCoordinates.value.timeout);
            hoverCoordinates.value.timeout = setTimeout(async () => {
                hoverLocationName.value = await queryLocationName(lat, lng);
            }, 500);
        }
    } catch (error) {
        console.error('Error handling map hover:', error);
    }
};

const handleMapMouseLeave = () => {
    if (hoverCoordinates.value?.timeout) {
        clearTimeout(hoverCoordinates.value.timeout);
    }
    hoverCoordinates.value = null;
    hoverLocationName.value = '';
};

// Submit navigation request using Leaflet Routing Machine
const submitNavigation = async () => {
    if (!canSubmit.value) return;

    isNavigating.value = true;
    navigationResults.value = null;

    try {
        // Check if Leaflet Routing Machine is available
        if (!L.Routing) {
            throw new Error('Leaflet Routing Machine not available. Please ensure it is properly installed.');
        }

        // Clear existing routing control to prevent multiple routes
        if (routingControl.value) {
            map.value.removeControl(routingControl.value);
            routingControl.value = null;
        }

        // Build waypoints array
        const waypoints = [];

        // Add from waypoint
        waypoints.push(L.latLng(navigationForm.value.from.latitude, navigationForm.value.from.longitude));

        // Add through points in order
        if (navigationForm.value.through.length > 0) {
            const sortedThrough = [...navigationForm.value.through]
                .filter(point => point.latitude && point.longitude)
                .sort((a, b) => a.index - b.index);

            sortedThrough.forEach(point => {
                waypoints.push(L.latLng(point.latitude, point.longitude));
            });
        }

        // Add to waypoint
        waypoints.push(L.latLng(navigationForm.value.to.latitude, navigationForm.value.to.longitude));

        console.log('Creating routing control with waypoints:', waypoints);

        // Get the correct router profile
        const getRouterProfile = () => {
            switch (navigationForm.value.mode) {
                case 'car': return 'driving';
                case 'bike': return 'cycling';
                case 'walk': return 'foot';
                default: return 'driving';
            }
        };

        // Create routing control with proper configuration using local OSRM
        routingControl.value = L.Routing.control({
            waypoints: waypoints,
            router: L.Routing.osrmv1({
                serviceUrl: `${window.location.origin}/osrm/route`,
                profile: getRouterProfile(),
                useHints: false,
                suppressDemoServerWarning: true,
                timeout: 30 * 1000
            }),
            routeWhileDragging: false,
            addWaypoints: false,
            draggableWaypoints: false,
            fitSelectedRoutes: true,
            show: false, // Don't show the default control panel
            createMarker: function(i, waypoint, n) {
                // Don't create default markers - we have our own
                return null;
            },
            lineOptions: {
                styles: [
                    { 
                        color: '#20B2AA', 
                        opacity: 0.8, 
                        weight: 6,
                        lineCap: 'round',
                        lineJoin: 'round'
                    }
                ]
            },
            altLineOptions: {
                styles: [
                    { 
                        color: '#7f8c8d', 
                        opacity: 0.6, 
                        weight: 4,
                        lineCap: 'round',
                        lineJoin: 'round'
                    }
                ]
            }
        });

        // Add event listeners
        routingControl.value.on('routingstart', function() {
            console.log('Routing started...');
            isNavigating.value = true;
        });

        routingControl.value.on('routesfound', function(e) {
            console.log('Routes found:', e.routes);
            isNavigating.value = false;

            // Process routes for our UI
            const processedRoutes = e.routes.map((route, index) => {
                const allSteps = [];
                let stepCounter = 0;

                // Extract steps from route instructions
                if (route.instructions) {
                    route.instructions.forEach((instruction) => {
                        allSteps.push({
                            index: stepCounter++,
                            distance: instruction.distance || 0,
                            duration: instruction.time || 0,
                            maneuver: {
                                type: instruction.type || 'continue',
                                modifier: instruction.modifier || '',
                                instruction: instruction.text || generateInstruction({
                                    type: instruction.type,
                                    modifier: instruction.modifier
                                }, navigationForm.value.language),
                                bearing_before: 0,
                                bearing_after: 0,
                                location: instruction.latLng ? [instruction.latLng.lng, instruction.latLng.lat] : []
                            },
                            name: instruction.road || '',
                            ref: '',
                            mode: navigationForm.value.mode
                        });
                    });
                }

                return {
                    index: index,
                    distance: route.summary?.totalDistance || 0,
                    duration: route.summary?.totalTime || 0,
                    summary: {
                        distance_text: formatDistance(route.summary?.totalDistance || 0),
                        duration_text: formatDuration(route.summary?.totalTime || 0),
                        mode: navigationForm.value.mode,
                        language: navigationForm.value.language
                    },
                    steps: allSteps,
                    coordinates: route.coordinates || []
                };
            });

            navigationResults.value = {
                code: 'Ok',
                routes: processedRoutes
            };

            selectedRouteIndex.value = 0;
            showDirections.value = true;

            console.log('Routes processed for UI:', processedRoutes);
        });

        routingControl.value.on('routeselected', function(e) {
            console.log('Route selected:', e.route);
        });

        routingControl.value.on('routingerror', function(e) {
            console.error('Routing error:', e.error);
            isNavigating.value = false;
            
            let errorMessage = 'Unable to find route';
            if (e.error?.message) {
                errorMessage += ': ' + e.error.message;
            } else if (e.error?.status) {
                errorMessage += ' (HTTP ' + e.error.status + ')';
            }
            
            alert(errorMessage + '. Please check your waypoints and try again.');
        });

        // Add to map
        routingControl.value.addTo(map.value);

    } catch (error) {
        console.error('Navigation setup failed:', error);
        alert('Navigation setup failed: ' + error.message + '. Please try again.');
        isNavigating.value = false;
    }
};

// Helper functions for local OSRM response processing
const formatDistance = (meters) => {
    if (meters < 1000) {
        return `${Math.round(meters)} m`;
    } else {
        return `${(meters / 1000).toFixed(1)} km`;
    }
};

const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
};

const generateInstruction = (maneuver, language = 'en') => {
    // Safety check for maneuver object
    if (!maneuver || typeof maneuver !== 'object') {
        return 'Continue straight';
    }

    const type = maneuver.type || 'continue';
    const modifier = maneuver.modifier || '';

    const instructions = {
        en: {
            'depart': 'Head',
            'turn': 'Turn',
            'new name': 'Continue',
            'arrive': 'Arrive',
            'merge': 'Merge',
            'on ramp': 'Take the ramp',
            'off ramp': 'Take the exit',
            'fork': 'Keep',
            'end of road': 'Continue',
            'continue': 'Continue',
            'roundabout': 'Enter the roundabout',
            'rotary': 'Enter the rotary',
            'roundabout turn': 'At the roundabout, take',
            'notification': 'Continue',
            'exit roundabout': 'Exit the roundabout'
        },
        fr: {
            'depart': 'Dirigez-vous',
            'turn': 'Tournez',
            'new name': 'Continuez',
            'arrive': 'Arrivée',
            'merge': 'Fusionnez',
            'on ramp': 'Prenez la bretelle',
            'off ramp': 'Prenez la sortie',
            'fork': 'Gardez',
            'end of road': 'Continuez',
            'continue': 'Continuez',
            'roundabout': 'Entrez dans le rond-point',
            'rotary': 'Entrez dans le rond-point',
            'roundabout turn': 'Au rond-point, prenez',
            'notification': 'Continuez',
            'exit roundabout': 'Sortez du rond-point'
        },
        rw: {
            'depart': 'Tangira',
            'turn': 'Hinda',
            'new name': 'Komeza',
            'arrive': 'Kigera',
            'merge': 'Huza',
            'on ramp': 'Fata ku nzira',
            'off ramp': 'Sohoka',
            'fork': 'Bana',
            'end of road': 'Komeza',
            'continue': 'Komeza',
            'roundabout': 'Injira mu kibuga',
            'rotary': 'Injira mu kibuga',
            'roundabout turn': 'Mu kibuga, fata',
            'notification': 'Komeza',
            'exit roundabout': 'Sohoka mu kibuga'
        }
    };

    const langInstructions = instructions[language] || instructions.en;
    const baseInstruction = langInstructions[type] || langInstructions['continue'];

    if (modifier) {
        const modifierText = {
            en: {
                'left': 'left',
                'right': 'right',
                'sharp left': 'sharp left',
                'sharp right': 'sharp right',
                'slight left': 'slight left',
                'slight right': 'slight right',
                'straight': 'straight'
            },
            fr: {
                'left': 'à gauche',
                'right': 'à droite',
                'sharp left': 'fortement à gauche',
                'sharp right': 'fortement à droite',
                'slight left': 'légèrement à gauche',
                'slight right': 'légèrement à droite',
                'straight': 'tout droit'
            },
            rw: {
                'left': 'ibumoso',
                'right': 'iburyo',
                'sharp left': 'ibumoso cyane',
                'sharp right': 'iburyo cyane',
                'slight left': 'ibumoso gato',
                'slight right': 'iburyo gato',
                'straight': 'imbere'
            }
        };

        const langModifiers = modifierText[language] || modifierText.en;
        const modifierInstruction = langModifiers[modifier] || '';

        return `${baseInstruction} ${modifierInstruction}`;
    }

    return baseInstruction;
};

// Route selection function for UI
const selectRoute = (routeIndex) => {
    selectedRouteIndex.value = routeIndex;

    // With Leaflet Routing Machine, we can trigger route selection
    if (routingControl.value && navigationResults.value?.routes[routeIndex]) {
        console.log('Selecting route:', routeIndex);
        // The routing control handles route display automatically
    }
};

// Clear navigation and routing control
const clearNavigation = () => {
    navigationResults.value = null;
    selectedRouteIndex.value = 0;
    showDirections.value = false;

    // Remove routing control from map
    if (routingControl.value && map.value) {
        map.value.removeControl(routingControl.value);
        routingControl.value = null;
    }
};

// Debug watcher
watch(panelsVisible, (newValue, oldValue) => {
    console.log('panelsVisible changed from', oldValue, 'to', newValue);
});

// Lifecycle hooks
onMounted(() => {
    initializeMap();
});

onUnmounted(() => {
    // Clean up routing control
    if (routingControl.value && map.value) {
        map.value.removeControl(routingControl.value);
        routingControl.value = null;
    }

    // Clean up map
    if (map.value) {
        map.value.remove();
    }
});
</script>

<template>
    <AppLayout title="Navigation">
        <div class="w-full h-screen relative">
        <!-- Map Container -->
        <div ref="mapContainer" class="w-full h-full"></div>

        <!-- Navigation Panel -->
        <div v-show="panelsVisible" class="absolute top-4 left-4 w-96 bg-white rounded-xl border-2 border-black shadow-lg z-[1000] max-h-[calc(100vh-2rem)] overflow-y-auto" style="z-index: 1000 !important; pointer-events: auto;">
            <div class="p-6" style="pointer-events: auto;">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-xl font-bold text-black">Navigation</h2>
                        <p class="text-sm text-gray-600">Rwanda Routes & Directions</p>
                    </div>
                    <button @click="panelsVisible = false" class="p-2 rounded-lg border border-black hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- From Location -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">From *</label>
                    <div v-if="navigationForm.from.name" class="p-3 bg-green-50 border-2 border-green-200 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-bold text-green-900">{{ navigationForm.from.name }}</div>
                                <div class="text-sm text-green-700">{{ navigationForm.from.latitude }}, {{ navigationForm.from.longitude }}</div>
                            </div>
                            <button @click="clearLocation('from')" class="text-green-600 hover:text-green-800 p-1 rounded-lg hover:bg-green-100 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div v-else class="space-y-3">
                        <div class="relative">
                            <Input
                                v-model="navigationForm.from.searchQuery"
                                @input="performLocationSearch('from', navigationForm.from.searchQuery)"
                                placeholder="Search starting location..."
                                class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                            />
                            <div v-if="isSearching.from" class="absolute right-3 top-1/2 -translate-y-1/2">
                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                            </div>

                            <!-- Search Results -->
                            <div v-if="searchResults.from.length > 0" class="absolute z-[1001] w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg" style="z-index: 1001 !important;">
                                <div v-for="result in searchResults.from" :key="`from-${result.type}-${result.id}`"
                                     @click="selectLocation('from', result)"
                                     class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                    <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                    <div class="text-sm text-gray-600">{{ result.type }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Selection Button -->
                        <div class="text-center">
                            <span class="text-sm text-gray-500">or</span>
                        </div>
                        <Button
                            @click="enableMapSelection('from')"
                            variant="outline"
                            class="w-full border-2 border-black text-black hover:bg-gray-100"
                            :class="{ 'bg-gray-100': mapSelectionMode === 'from' }"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ mapSelectionMode === 'from' ? 'Click on map to select' : 'Select from map' }}
                        </Button>
                    </div>
                </div>

                <!-- To Location -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">To *</label>
                    <div v-if="navigationForm.to.name" class="p-3 bg-red-50 border-2 border-red-200 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-bold text-red-900">{{ navigationForm.to.name }}</div>
                                <div class="text-sm text-red-700">{{ navigationForm.to.latitude }}, {{ navigationForm.to.longitude }}</div>
                            </div>
                            <button @click="clearLocation('to')" class="text-red-600 hover:text-red-800 p-1 rounded-lg hover:bg-red-100 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div v-else class="space-y-3">
                        <div class="relative">
                            <Input
                                v-model="navigationForm.to.searchQuery"
                                @input="performLocationSearch('to', navigationForm.to.searchQuery)"
                                placeholder="Search destination..."
                                class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                            />
                            <div v-if="isSearching.to" class="absolute right-3 top-1/2 -translate-y-1/2">
                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                            </div>

                            <!-- Search Results -->
                            <div v-if="searchResults.to.length > 0" class="absolute z-[1001] w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg" style="z-index: 1001 !important;">
                                <div v-for="result in searchResults.to" :key="`to-${result.type}-${result.id}`"
                                     @click="selectLocation('to', result)"
                                     class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                    <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                    <div class="text-sm text-gray-600">{{ result.type }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Selection Button -->
                        <div class="text-center">
                            <span class="text-sm text-gray-500">or</span>
                        </div>
                        <Button
                            @click="enableMapSelection('to')"
                            variant="outline"
                            class="w-full border-2 border-black text-black hover:bg-gray-100"
                            :class="{ 'bg-gray-100': mapSelectionMode === 'to' }"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ mapSelectionMode === 'to' ? 'Click on map to select' : 'Select from map' }}
                        </Button>
                    </div>
                </div>

                <!-- Through Points -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <label class="block text-sm font-bold text-black">Through Points</label>
                        <Button @click="addThroughPoint" variant="outline" size="sm" class="border-black text-black hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Add Stop
                        </Button>
                    </div>

                    <div v-if="throughPointsOrdered.length === 0" class="text-sm text-gray-500 italic">
                        No stops added. Click "Add Stop" to add waypoints.
                    </div>

                    <div v-for="(point, index) in throughPointsOrdered" :key="point.index" class="mb-3">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="text-xs font-bold text-black bg-gray-200 rounded-full w-6 h-6 flex items-center justify-center">{{ index + 1 }}</span>
                            <span class="text-sm font-medium text-black">Stop {{ index + 1 }}</span>
                        </div>

                        <div v-if="point.name" class="p-3 bg-blue-50 border-2 border-blue-200 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-bold text-blue-900">{{ point.name }}</div>
                                    <div class="text-sm text-blue-700">{{ point.latitude }}, {{ point.longitude }}</div>
                                </div>
                                <button @click="clearLocation('through', point.index)" class="text-blue-600 hover:text-blue-800 p-1 rounded-lg hover:bg-blue-100 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div v-else class="space-y-3">
                            <div class="relative">
                                <Input
                                    v-model="point.searchQuery"
                                    @input="performLocationSearch('through', point.searchQuery, point.index)"
                                    :placeholder="`Search stop ${index + 1}...`"
                                    class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                                />
                                <div v-if="isSearching.through[point.index]" class="absolute right-3 top-1/2 -translate-y-1/2">
                                    <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                                </div>

                                <!-- Search Results -->
                                <div v-if="searchResults.through[point.index]?.length > 0" class="absolute z-[1001] w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg" style="z-index: 1001 !important;">
                                    <div v-for="result in searchResults.through[point.index]" :key="`through-${point.index}-${result.type}-${result.id}`"
                                         @click="selectLocation('through', result, point.index)"
                                         class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                        <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                        <div class="text-sm text-gray-600">{{ result.type }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Map Selection Button -->
                            <div class="text-center">
                                <span class="text-sm text-gray-500">or</span>
                            </div>
                            <Button
                                @click="enableMapSelection('through', point.index)"
                                variant="outline"
                                class="w-full border-2 border-black text-black hover:bg-gray-100"
                                :class="{ 'bg-gray-100': mapSelectionMode === point.index }"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {{ mapSelectionMode === point.index ? 'Click on map to select' : 'Select from map' }}
                            </Button>
                        </div>
                    </div>
                </div>

                <!-- Mode Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">Travel Mode *</label>
                    <Select v-model="navigationForm.mode">
                        <SelectTrigger class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0">
                            <SelectValue placeholder="Select travel mode" />
                        </SelectTrigger>
                        <SelectContent class="border-2 border-black rounded-lg">
                            <SelectItem value="car">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 6h3l2 7H8l2-7h3z" />
                                    </svg>
                                    Car
                                </div>
                            </SelectItem>
                            <SelectItem value="bike">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="18.5" cy="17.5" r="3.5"/>
                                        <circle cx="5.5" cy="17.5" r="3.5"/>
                                        <circle cx="15" cy="5" r="1"/>
                                        <path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                    </svg>
                                    Bike
                                </div>
                            </SelectItem>
                            <SelectItem value="walk">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Walk
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <!-- Language Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">Language *</label>
                    <Select v-model="navigationForm.language">
                        <SelectTrigger class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0">
                            <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent class="border-2 border-black rounded-lg">
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="fr">Français</SelectItem>
                            <SelectItem value="rw">Kinyarwanda</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <!-- Submit Button -->
                <Button
                    @click="submitNavigation"
                    :disabled="!canSubmit || isNavigating"
                    class="w-full rounded-lg border-2 border-black bg-black text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <div v-if="isNavigating" class="flex items-center gap-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        Getting Directions...
                    </div>
                    <span v-else>Get Directions</span>
                </Button>

                <!-- Clear Navigation Button -->
                <Button
                    v-if="navigationResults"
                    @click="clearNavigation"
                    variant="outline"
                    class="w-full mt-3 rounded-lg border-2 border-black text-black hover:bg-gray-100"
                >
                    Clear Route
                </Button>
            </div>
        </div>

        <!-- Navigation Results Panel -->
        <div v-if="navigationResults && showDirections" class="absolute top-4 right-4 w-96 bg-white rounded-xl border-2 border-black shadow-lg z-[1000] max-h-[calc(100vh-2rem)] overflow-y-auto" style="z-index: 1000 !important;">
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-black">Directions</h2>
                    <button @click="showDirections = false" class="p-2 rounded-lg border border-black hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Route Options -->
                <div v-if="navigationResults.routes.length > 1" class="mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-sm font-bold text-black">Route Options</h3>
                    </div>
                    <div class="space-y-2">
                        <div v-for="(route, index) in navigationResults.routes" :key="index"
                             @click="selectRoute(index)"
                             class="p-3 rounded-lg border-2 cursor-pointer transition-colors"
                             :class="selectedRouteIndex === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-bold text-black">Route {{ index + 1 }}</div>
                                    <div class="text-sm text-gray-600">
                                        {{ route.summary.distance_text }} • {{ route.summary.duration_text }}
                                    </div>
                                </div>
                                <div v-if="selectedRouteIndex === index" class="text-blue-500">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Selected Route Summary -->
                <div v-if="navigationResults.routes[selectedRouteIndex]" class="mb-6">
                    <h3 class="text-sm font-bold text-black mb-3">Route Summary</h3>
                    <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <div class="text-xs text-gray-500">Distance</div>
                                <div class="font-bold text-black">{{ navigationResults.routes[selectedRouteIndex].summary.distance_text }}</div>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500">Duration</div>
                                <div class="font-bold text-black">{{ navigationResults.routes[selectedRouteIndex].summary.duration_text }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Turn-by-Turn Directions -->
                <div v-if="navigationResults.routes[selectedRouteIndex]?.steps" class="mb-6">
                    <h3 class="text-sm font-bold text-black mb-3">Turn-by-Turn Directions</h3>
                    <div class="space-y-2">
                        <div v-for="step in navigationResults.routes[selectedRouteIndex].steps" :key="step.index"
                             class="flex gap-3 p-3 rounded-lg border border-gray-200 hover:bg-teal-50 transition-colors">
                            <div class="flex-shrink-0 w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                                <span class="text-xs font-bold text-teal-700">{{ step.index + 1 }}</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="font-medium text-black">{{ step.maneuver?.instruction }}</div>
                                <div class="text-xs text-gray-500 mt-1 space-y-1">
                                    <div>
                                        {{ formatDistance(step.distance) }} • {{ formatDuration(step.duration) }}
                                    </div>
                                    <div v-if="step.name || step.ref" class="flex items-center space-x-2">
                                        <span v-if="step.ref" class="px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded text-xs font-mono">{{ step.ref }}</span>
                                        <span v-if="step.name" class="text-gray-600">{{ step.name }}</span>
                                    </div>
                                    <div v-if="step.maneuver?.type" class="text-xs text-gray-400">
                                        {{ step.maneuver.type }}{{ step.maneuver.modifier ? ` ${step.maneuver.modifier}` : '' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toggle Panel Button -->
        <button v-if="!panelsVisible" @click="panelsVisible = true"
                class="absolute top-4 left-4 p-3 bg-white rounded-lg border-2 border-black hover:bg-gray-100 transition-colors z-[1000]" style="z-index: 1000 !important;">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>

        <!-- Map Selection Mode Overlay -->
        <div v-if="mapSelectionMode !== null" class="absolute top-4 right-4 bg-white rounded-lg border-2 border-black p-4 z-[1001] max-w-sm" style="z-index: 1001 !important;">
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-bold text-black">
                    {{ typeof mapSelectionMode === 'number' ? `Select Stop ${mapSelectionMode + 1}` : `Select ${mapSelectionMode} location` }}
                </h3>
                <button @click="disableMapSelection" class="text-black hover:text-gray-600 p-1 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-600 mb-3">Click anywhere on the map to select a location</p>

            <!-- Hover Coordinates Display -->
            <div v-if="hoverCoordinates" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <div class="text-xs text-gray-500 mb-1">Hover Location:</div>
                <div class="text-sm font-mono text-black">
                    {{ hoverCoordinates.latitude.toFixed(6) }}, {{ hoverCoordinates.longitude.toFixed(6) }}
                </div>
                <div v-if="hoverLocationName" class="text-sm text-gray-700 mt-1">
                    {{ hoverLocationName }}
                </div>
                <div v-else-if="isLoadingHoverLocation" class="text-sm text-gray-500 mt-1">
                    Loading location name...
                </div>
            </div>

            <Button @click="disableMapSelection" variant="outline" class="w-full mt-3 border-black text-black hover:bg-gray-100">
                Cancel Selection
            </Button>
        </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Ensure form panels stay above map */
.absolute {
    pointer-events: auto;
}

/* Prevent map from interfering with form interactions */
.leaflet-container {
    z-index: 1 !important;
}

/* Ensure form elements are interactive */
.absolute > div {
    pointer-events: auto;
}

/* Custom marker styles */
.custom-marker {
    background: none !important;
    border: none !important;
}

/* Hide default Leaflet Routing Machine control panel */
.leaflet-routing-container {
    display: none !important;
}

/* Ensure dropdowns appear above everything */
.absolute.z-\[1001\] {
    z-index: 1001 !important;
}

.absolute.z-\[1000\] {
    z-index: 1000 !important;
}

/* Map bounds indicator */
.leaflet-container {
    border: 2px solid #20B2AA;
    border-radius: 8px;
}

/* Route line styling - prevent multiple overlapping routes */
.leaflet-routing-alt {
    display: none !important;
}

/* Clean route display */
.leaflet-routing-line {
    stroke: #20B2AA !important;
    stroke-width: 6px !important;
    stroke-linecap: round !important;
    stroke-linejoin: round !important;
    opacity: 0.8 !important;
}

/* Alternative route styling */
.leaflet-routing-alt-line {
    stroke: #7f8c8d !important;
    stroke-width: 4px !important;
    opacity: 0.6 !important;
}
</style>